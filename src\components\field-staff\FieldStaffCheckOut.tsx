import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  LogOut, 
  Clock, 
  MapPin, 
  AlertCircle,
  CheckCircle,
  Loader2,
  FileText,
  Calendar
} from 'lucide-react';
import { useGPSLocation, getDeviceInfo, getNetworkInfo, formatLocation } from '@/hooks/field-staff/useGPSLocation';
import { useFieldStaffCheckOut, useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { useSchools } from '@/hooks/useSchools';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import FieldReportForm from './FieldReportForm';
import OfflineStatusIndicator from './OfflineStatusIndicator';
import AttendanceSyncHelper from './AttendanceSyncHelper';
import { Database } from '@/integrations/supabase/types';
import { toast } from 'sonner';

type FieldActivityType = Database['public']['Enums']['field_activity_type'];

interface FieldReportData {
  activity_type: FieldActivityType;
  round_table_sessions_count: number;
  total_students_attended: number;
  students_per_session: number;
  activities_conducted: string[];
  topics_covered: string[];
  challenges_encountered: string;
  wins_achieved: string;
  general_observations: string;
  lessons_learned: string;
  follow_up_required: boolean;
  follow_up_actions: string;
}

const FieldStaffCheckOut: React.FC = () => {
  const [showReportForm, setShowReportForm] = useState(false);
  const [isOfflineMode, setIsOfflineMode] = useState(false);
  const [showSyncHelper, setShowSyncHelper] = useState(false);
  
  const { 
    location, 
    error: locationError, 
    loading: locationLoading, 
    getCurrentLocation,
    isSupported: gpsSupported 
  } = useGPSLocation();
  
  const checkOutMutation = useFieldStaffCheckOut();
  const { data: currentAttendance, isLoading: attendanceLoading } = useCurrentAttendance();
  const { data: unifiedStatus } = useUnifiedCheckInStatus();
  const { data: schools } = useSchools();

  // Check if user is checked in using unified status
  const isCheckedIn = unifiedStatus?.isCheckedIn ?? false;
  const currentSchool = schools?.find(school => school.id === (unifiedStatus?.schoolId || currentAttendance?.school_id));

  // Auto-detect offline mode
  useEffect(() => {
    const handleOnlineStatus = () => {
      setIsOfflineMode(!navigator.onLine);
    };

    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);
    
    setIsOfflineMode(!navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, []);

  const calculateDuration = () => {
    if (!currentAttendance?.check_in_time) return 0;
    return Math.round((Date.now() - new Date(currentAttendance.check_in_time).getTime()) / (1000 * 60));
  };

  const handleStartCheckOut = async () => {
    // Location is no longer required to start check-out process
    // GPS will be attempted during the actual check-out submission
    setShowReportForm(true);
  };

  const handleSubmitReport = async (reportData: FieldReportData) => {
    if (!currentAttendance) {
      toast.error('No active check-in found');
      return;
    }

    try {
      // Location is now optional for check-out
      // Try to get current location but don't fail if unavailable
      let currentLocation: LocationData | null = location;
      if (!currentLocation) {
        try {
          currentLocation = await getCurrentLocation({
            enableHighAccuracy: false, // Use lower accuracy for optional location
            timeout: 5000, // Short timeout since it's optional
            maximumAge: 600000, // 10 minutes cache is acceptable
          });
        } catch (gpsError) {
          // GPS failure is acceptable for check-out
          console.log('GPS not available for check-out, proceeding without location');
        }
      }

      await checkOutMutation.mutateAsync({
        attendance_id: currentAttendance.id,
        // Location parameters are now optional
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude,
        accuracy: currentLocation?.accuracy,
        offline_sync: isOfflineMode,
        // Map field names from FieldReportForm to CheckOutData interface
        activity_type: reportData.activity_type,
        round_table_sessions: reportData.round_table_sessions_count,
        total_students: reportData.total_students_attended,
        students_per_session: reportData.students_per_session,
        activities_conducted: reportData.activities_conducted,
        topics_covered: reportData.topics_covered,
        challenges: reportData.challenges_encountered,
        wins: reportData.wins_achieved,
        observations: reportData.general_observations,
        lessons_learned: reportData.lessons_learned,
        follow_up_required: reportData.follow_up_required,
        follow_up_actions: reportData.follow_up_actions,
      });

      setShowReportForm(false);
      toast.success('Successfully checked out and submitted field report!');
    } catch (error: unknown) {
      console.error('Check-out failed:', error);

      // Check if this is an attendance record not found error
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (errorMessage.includes('Attendance record not found') || errorMessage.includes('not found')) {
        toast.error('Attendance sync issue detected. Opening diagnostic tool...');
        setShowSyncHelper(true);
      } else {
        toast.error('Check-out failed. Please try again.');
      }
    }
  };

  if (attendanceLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </PageLayout>
    );
  }

  if (!isCheckedIn) {
    return (
      <PageLayout>
        <PageHeader
          title="Field Staff Check-Out"
          description="Complete your field work and submit your daily report"
        />
        
        <ContentCard>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Not Checked In
            </h3>
            <p className="text-gray-600 mb-4">
              You need to check in first before you can check out.
            </p>
            <Button onClick={() => window.location.href = '/field-staff/check-in'}>
              Go to Check-In
            </Button>
          </div>
        </ContentCard>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Field Staff Check-Out"
        description="Complete your field work and submit your daily report"
      />

      {/* Current Session Info */}
      <ContentCard>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Current Session
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm font-medium text-gray-600">School</div>
              <div className="text-lg font-medium">
                {currentSchool?.name || 'Unknown School'}
              </div>
              <div className="text-sm text-gray-500">
                {currentSchool?.district}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-600">Check-in Time</div>
              <div className="text-lg">
                {currentAttendance && new Date(currentAttendance.check_in_time).toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-500">
                {currentAttendance && new Date(currentAttendance.check_in_time).toLocaleDateString()}
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium text-gray-600">Duration</div>
              <div className="text-lg font-medium text-green-600">
                {calculateDuration()} minutes
              </div>
              <div className="text-sm text-gray-500">
                {Math.round(calculateDuration() / 60 * 10) / 10} hours
              </div>
            </div>
          </div>
        </CardContent>
      </ContentCard>

      {/* Offline Status */}
      <OfflineStatusIndicator showDetails={true} />

      {!showReportForm ? (
        /* Check-Out Initiation */
        <ContentCard>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LogOut className="h-5 w-5" />
              Ready to Check Out?
            </CardTitle>
            <CardDescription>
              Before checking out, you'll need to complete a field report about today's activities.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Location Status */}
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              {locationLoading ? (
                <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
              ) : location ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <MapPin className="h-5 w-5 text-gray-500" />
              )}
              
              <div className="flex-1">
                <div className="text-sm font-medium">
                  {location ? 'Location Confirmed' : 'Location Required'}
                </div>
                <div className="text-xs text-gray-600">
                  {location 
                    ? `${formatLocation(location)} (±${Math.round(location.accuracy)}m)`
                    : 'GPS location will be captured for check-out'
                  }
                </div>
              </div>
            </div>

            {/* Requirements */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">Before checking out:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Complete field report with activity details</li>
                <li>• Record student attendance numbers</li>
                <li>• Document challenges and successes</li>
                <li>• Note any follow-up actions needed</li>
              </ul>
            </div>

            <Button
              onClick={handleStartCheckOut}
              disabled={locationLoading}
              className="w-full"
              size="lg"
            >
              {locationLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Getting Location...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Start Check-Out Process
                </>
              )}
            </Button>

            {/* Debug button for sync issues */}
            <Button
              variant="outline"
              onClick={() => setShowSyncHelper(true)}
              className="w-full mt-2"
              size="sm"
            >
              Troubleshoot Sync Issues
            </Button>
          </CardContent>
        </ContentCard>
      ) : (
        /* Field Report Form */
        <ContentCard>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Daily Field Report
            </CardTitle>
            <CardDescription>
              Complete this report to document today's activities and check out.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FieldReportForm
              onSubmit={handleSubmitReport}
              isSubmitting={checkOutMutation.isPending}
            />
            
            <div className="mt-4 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowReportForm(false)}
                disabled={checkOutMutation.isPending}
              >
                Cancel Check-Out
              </Button>
            </div>
          </CardContent>
        </ContentCard>
      )}

      {/* Attendance Sync Helper Modal */}
      {showSyncHelper && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <AttendanceSyncHelper onClose={() => setShowSyncHelper(false)} />
        </div>
      )}
    </PageLayout>
  );
};

export default FieldStaffCheckOut;
