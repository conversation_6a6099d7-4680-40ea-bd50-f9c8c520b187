import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MapPin,
  Calendar,
  Clock,
  Users,
  CheckCircle,
  Navigation,
  Plus,
  Timer,
  Target,
  BookOpen,
  Eye,
  FileText,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { useAttendanceSessions } from '@/hooks/attendance/useAttendanceSessions';
import { Database } from '@/types/database.types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type AttendanceSession = Database['public']['Tables']['attendance_sessions']['Row'] & {
  school?: { name: string };
  facilitator?: { name: string };
  student_attendance?: Database['public']['Tables']['student_attendance']['Row'][];
};
import GPSCheckIn from './GPSCheckIn';
import SessionManagement from './SessionManagement';
import CreateSessionDialog from './CreateSessionDialog';
import FieldStaffCheckIn from '../field-staff/FieldStaffCheckIn';
import FieldStaffCheckOut from '../field-staff/FieldStaffCheckOut';
import ConsolidatedStaffReports from './ConsolidatedStaffReports';
import UnifiedAttendanceAnalytics from './UnifiedAttendanceAnalytics';

const UnifiedFieldVisits = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('check-in');
  
  // Check current status using unified hook
  const { data: unifiedStatus, isLoading: statusLoading } = useUnifiedCheckInStatus();

  // Memoize today's date range to prevent infinite re-renders
  const todayDateRange = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of day
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999); // End of day
    return {
      start: today,
      end: endOfDay
    };
  }, []); // Empty dependency array since we want today's date to be stable during the component lifecycle

  const { data: todaySessions } = useAttendanceSessions(undefined, todayDateRange);

  // Type cast sessions data to ensure proper typing
  const typedSessions = todaySessions as AttendanceSession[] | undefined;

  // Role-based permissions
  const isFieldStaff = profile?.role === 'field_staff';
  const canManageSessions = profile?.role === 'admin' || profile?.role === 'program_officer';

  // Determine current status from unified source
  const isCheckedIn = unifiedStatus?.isCheckedIn ?? false;
  const hasActiveSessions = typedSessions && typedSessions.length > 0;

  // Get status info for header
  const getStatusInfo = () => {
    if (isCheckedIn && unifiedStatus) {
      const location = unifiedStatus.schoolName || 'Unknown location';
      const timeStr = unifiedStatus.checkInTime ? new Date(unifiedStatus.checkInTime).toLocaleTimeString() : '';
      return {
        status: 'checked-in',
        message: `Checked in at ${location} ${timeStr ? `at ${timeStr}` : ''}`,
        color: 'bg-green-100 text-green-800'
      };
    }

    if (hasActiveSessions) {
      return {
        status: 'sessions-available',
        message: `${todaySessions?.length} session(s) scheduled for today`,
        color: 'bg-blue-100 text-blue-800'
      };
    }

    return {
      status: 'ready',
      message: 'Ready for field visits',
      color: 'bg-gray-100 text-gray-800'
    };
  };

  const statusInfo = getStatusInfo();

  // Tab configuration based on status (unified for all roles)
  const getAvailableTabs = () => {
    const tabs = [];

    // Check-in/Check-out tab (unified for all roles)
    tabs.push({
      id: 'check-in',
      label: isCheckedIn ? 'Check Out & Report' : 'Check In',
      icon: isCheckedIn ? CheckCircle : MapPin,
      badge: isCheckedIn ? 'Active' : null
    });

    // Sessions tab
    tabs.push({
      id: 'sessions',
      label: 'Sessions',
      icon: Calendar,
      badge: hasActiveSessions ? typedSessions?.length.toString() : null
    });

    // Staff Reports tab (for admin and program officers only)
    if (canManageSessions) {
      tabs.push({
        id: 'staff-reports',
        label: 'Staff Reports',
        icon: Users,
        badge: null
      });
    }

    // Analytics tab (for admin and program officers only)
    if (canManageSessions) {
      tabs.push({
        id: 'analytics',
        label: 'Analytics',
        icon: BarChart3,
        badge: null
      });
    }

    return tabs;
  };

  const availableTabs = getAvailableTabs();

  return (
    <PageLayout>
      <PageHeader
        title={isFieldStaff ? 'Field Visits' : 'Field Visit Management'}
        description={isFieldStaff 
          ? 'Check in/out and manage your field visit sessions'
          : 'GPS check-in and session management for field visits'
        }
        icon={Navigation}
      >
        {/* Status Badge */}
        <Badge className={statusInfo.color}>
          {statusInfo.message}
        </Badge>
      </PageHeader>

      <ContentCard>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <TabsList className={`grid w-full ${availableTabs.length <= 2 ? 'grid-cols-2' : availableTabs.length <= 3 ? 'grid-cols-3' : availableTabs.length <= 4 ? 'grid-cols-4' : 'grid-cols-5'} sm:w-auto`}>
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span>{tab.label}</span>
                    {tab.badge && (
                      <Badge variant="secondary" className="ml-1">
                        {tab.badge}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {/* Tab Content */}
          <TabsContent value="check-in" className="mt-0">
            {/* Unified Check-in/Check-out interface for all roles */}
            <div className="space-y-6">
              {!isFieldStaff && (
                <Alert>
                  <Navigation className="h-4 w-4" />
                  <AlertDescription>
                    Use the comprehensive check-in system to track field visits and submit detailed reports.
                  </AlertDescription>
                </Alert>
              )}

              {isCheckedIn ? (
                // Show check-out interface for all roles
                <FieldStaffCheckOut />
              ) : (
                // Show check-in interface for all roles
                <FieldStaffCheckIn />
              )}
            </div>
          </TabsContent>

          <TabsContent value="sessions" className="mt-0">
            {isFieldStaff ? (
              // Field Staff: Simplified sessions view
              <div className="space-y-6">
                {/* Session Overview Cards - Field Staff View */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Today's Sessions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{typedSessions?.length || 0}</div>
                      <p className="text-xs text-muted-foreground">
                        Scheduled for today
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Active Now
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {typedSessions?.filter(s => {
                          const now = new Date();
                          const sessionStart = new Date(`${s.session_date}T${s.start_time}`);
                          const sessionEnd = s.end_time
                            ? new Date(`${s.session_date}T${s.end_time}`)
                            : new Date(sessionStart.getTime() + (s.planned_duration_minutes || 60) * 60000);
                          return now >= sessionStart && now <= sessionEnd;
                        }).length || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Currently running
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Field Staff Sessions List */}
                <ContentCard>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Calendar className="h-5 w-5" />
                          My Sessions Today
                        </CardTitle>
                        <CardDescription>
                          Sessions you're facilitating or attending today
                        </CardDescription>
                      </div>
                      <CreateSessionDialog
                        trigger={
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Create Session
                          </Button>
                        }
                      />
                    </div>
                  </CardHeader>
                  <CardContent>
                    {typedSessions && typedSessions.length > 0 ? (
                      <div className="space-y-3">
                        {typedSessions
                          .filter(session =>
                            // Show sessions where field staff is facilitator or if no specific facilitator
                            session.facilitator_id === profile?.id || !session.facilitator_id
                          )
                          .map((session) => {
                            const sessionStart = new Date(`${session.session_date}T${session.start_time}`);
                            const sessionEnd = session.end_time
                              ? new Date(`${session.session_date}T${session.end_time}`)
                              : new Date(sessionStart.getTime() + (session.planned_duration_minutes || 60) * 60000);
                            const now = new Date();
                            const isActive = now >= sessionStart && now <= sessionEnd;
                            const isPast = now > sessionEnd;

                            return (
                              <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <h4 className="font-medium">{session.session_name}</h4>
                                    <Badge variant={isActive ? "default" : isPast ? "secondary" : "outline"}>
                                      {isActive ? "Active" : isPast ? "Completed" : "Upcoming"}
                                    </Badge>
                                  </div>
                                  <div className="text-sm text-muted-foreground space-y-1">
                                    <div className="flex items-center gap-2">
                                      <Clock className="h-4 w-4" />
                                      {sessionStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                      {session.end_time && ` - ${sessionEnd.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`}
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <Users className="h-4 w-4" />
                                      {session.session_type} • {session.max_capacity || 0} students
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      // Navigate to attendance tracking for this session
                                      // This would be handled by the parent component
                                    }}
                                  >
                                    <Eye className="h-4 w-4 mr-2" />
                                    View
                                  </Button>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No Sessions Today</h3>
                        <p className="text-gray-600 mb-4">
                          You don't have any sessions scheduled for today.
                        </p>
                        <CreateSessionDialog
                          trigger={
                            <Button>
                              <Plus className="h-4 w-4 mr-2" />
                              Create Your First Session
                            </Button>
                          }
                        />
                      </div>
                    )}
                  </CardContent>
                </ContentCard>
              </div>
            ) : (
              // Admin/Program Officer: Full session management
              <div className="space-y-6">
                {/* Session Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Today's Sessions
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{typedSessions?.length || 0}</div>
                      <p className="text-xs text-muted-foreground">
                        Scheduled for today
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Active Now
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {typedSessions?.filter(s => {
                          const now = new Date();
                          const sessionStart = new Date(`${s.session_date}T${s.start_time}`);
                          const sessionEnd = s.end_time
                            ? new Date(`${s.session_date}T${s.end_time}`)
                            : new Date(sessionStart.getTime() + (s.planned_duration_minutes || 60) * 60000);
                          return now >= sessionStart && now <= sessionEnd;
                        }).length || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Currently running
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Total Capacity
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {typedSessions?.reduce((sum, s) => sum + (s.max_capacity || 0), 0) || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Students expected
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Session Management Interface */}
                <SessionManagement />
              </div>
            )}
          </TabsContent>

          {/* Staff Reports Tab - Admin and Program Officer only */}
          {canManageSessions && (
            <TabsContent value="staff-reports" className="mt-0">
              <div className="space-y-6">
                <Alert>
                  <Users className="h-4 w-4" />
                  <AlertDescription>
                    Comprehensive staff reporting dashboard for timesheets, field reports, and notifications.
                  </AlertDescription>
                </Alert>

                <ConsolidatedStaffReports />
              </div>
            </TabsContent>
          )}

          {/* Analytics Tab - Admin and Program Officer only */}
          {canManageSessions && (
            <TabsContent value="analytics" className="mt-0">
              <div className="space-y-6">
                <Alert>
                  <BarChart3 className="h-4 w-4" />
                  <AlertDescription>
                    Advanced analytics for field staff productivity, school visit patterns, and attendance trends.
                  </AlertDescription>
                </Alert>

                <UnifiedAttendanceAnalytics />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </ContentCard>
    </PageLayout>
  );
};

export default UnifiedFieldVisits;
