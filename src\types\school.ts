
export interface ContactInfo {
  id: string;
  name: string;
  phone?: string;
  email?: string; // Made optional for champion teachers
}

export interface School {
  id: string;
  name: string;
  code?: string;
  school_type: 'primary' | 'secondary' | 'tertiary' | 'vocational' | 'university';
  school_category?: 'day' | 'boarding' | 'both';
  student_count?: number;
  teacher_count?: number;
  contact_phone?: string;
  email?: string;
  district?: string;
  sub_county?: string;
  registration_status?: 'registered' | 'pending' | 'unregistered' | 'inactive';
  classes_count?: number;
  streams_per_class?: number;
  head_teacher_name?: string;
  head_teacher_phone?: string;
  head_teacher_email?: string; // Made optional
  deputy_head_teacher_name?: string;
  deputy_head_teacher_phone?: string;
  deputy_head_teacher_email?: string; // Made optional
  year_established?: number;
  date_joined_ilead?: string;
  ownership_type?: string;
  location_description?: string;
  location_coordinates?: string;
  nearest_health_center?: string;
  distance_to_main_road?: string;
  infrastructure_notes?: string;
  is_partner_managed?: boolean;
  partner_name?: string;
  champion_teacher_count?: number;
  champion_teachers?: ContactInfo[];
  assistant_champion_teachers?: ContactInfo[];
  created_at?: string;
  updated_at?: string;
  division_id?: string;
  created_by?: string;
  total_count?: number;
}

export interface SchoolFilters {
  search?: string;
  school_type?: School['school_type'];
  registration_status?: School['registration_status'];
  district?: string;
}

export interface SchoolSort {
  field: 'name' | 'student_count' | 'teacher_count' | 'created_at' | 'registration_status';
  direction: 'asc' | 'desc';
}

export interface SchoolStatistics {
  total_schools: number;
  by_type: Record<string, number>;
  by_status: Record<string, number>;
  by_district: Record<string, number>;
  total_students: number;
  total_teachers: number;
  avg_student_teacher_ratio: number;
}

export interface SchoolFormData {
  // Required fields
  name: string;
  school_type: School['school_type'];
  division_id: string; // District is required (changed from division to district)
  champion_teachers: ContactInfo[]; // At least 1 champion teacher required

  // Optional fields
  code?: string; // Made optional
  school_category?: School['school_category'];
  student_count?: number;
  teacher_count?: number; // Made optional
  contact_phone?: string;
  email?: string;
  classes_count?: number;
  streams_per_class?: number;
  head_teacher_name?: string;
  head_teacher_phone?: string;
  head_teacher_email?: string; // Made optional
  deputy_head_teacher_name?: string;
  deputy_head_teacher_phone?: string;
  deputy_head_teacher_email?: string; // Made optional
  year_established?: number;
  date_joined_ilead?: string; // Made optional
  ownership_type?: string;
  location_description?: string;
  location_coordinates?: string;
  nearest_health_center?: string;
  distance_to_main_road?: string;
  infrastructure_notes?: string;
  is_partner_managed?: boolean;
  partner_name?: string;
  champion_teacher_count?: number;
  assistant_champion_teachers?: ContactInfo[]; // Made optional (can add multiple)
  field_staff_id?: string;
  registration_status?: School['registration_status'];
}

export interface SchoolAuditLog {
  id: string;
  school_id: string;
  action: 'created' | 'updated' | 'deleted' | 'status_changed';
  old_values?: Record<string, unknown>;
  new_values?: Record<string, unknown>;
  changed_by?: string;
  changed_at: string;
}
