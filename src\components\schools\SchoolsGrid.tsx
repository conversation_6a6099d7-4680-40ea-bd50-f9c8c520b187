
import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MoreVertical, 
  Users, 
  GraduationCap, 
  Phone, 
  Mail, 
  MapPin,
  Edit,
  Eye,
  Trash2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { School } from '@/types/school';

interface SchoolsGridProps {
  schools: School[];
  selectedSchools: string[];
  onSelectSchool: (schoolId: string, selected: boolean) => void;
  onViewDetails: (school: School) => void;
  onEditSchool: (school: School) => void;
  onDeleteSchool: (school: School) => void;
  onToggleActive: (school: School) => void;
  canManageSchools: boolean;
  isAdmin: boolean;
}

const SchoolsGrid = ({
  schools,
  selectedSchools,
  onSelectSchool,
  onViewDetails,
  onEditSchool,
  onDeleteSchool,
  onToggleActive,
  canManageSchools,
  isAdmin
}: SchoolsGridProps) => {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'registered': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSchoolTypeColor = (type: string) => {
    switch (type) {
      case 'primary': return 'bg-blue-100 text-blue-800';
      case 'secondary': return 'bg-purple-100 text-purple-800';
      case 'tertiary': return 'bg-indigo-100 text-indigo-800';
      case 'vocational': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (schools.length === 0) {
    return (
      <div className="text-center py-12">
        <GraduationCap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No schools found</h3>
        <p className="text-gray-600">Try adjusting your search or filter criteria</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {schools.map((school) => (
        <Card key={school.id} className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedSchools.includes(school.id)}
                  onCheckedChange={(checked) => onSelectSchool(school.id, !!checked)}
                />
                <div className="flex-1">
                  <h3 className="font-semibold text-lg leading-tight">{school.name}</h3>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onViewDetails(school)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  {canManageSchools && (
                    <>
                      <DropdownMenuItem onClick={() => onEditSchool(school)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onToggleActive(school)}>
                        {school.registration_status === 'inactive' ? 'Mark as Active' : 'Mark as Inactive'}
                      </DropdownMenuItem>
                      {isAdmin && (
                        <DropdownMenuItem 
                          onClick={() => onDeleteSchool(school)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            {/* Badges */}
            <div className="flex flex-wrap gap-2">
              <Badge className={getSchoolTypeColor(school.school_type)}>
                {school.school_type}
              </Badge>
              {school.registration_status && (
                <Badge className={getStatusColor(school.registration_status)}>
                  {school.registration_status === 'registered' ? 'active' :
                   school.registration_status === 'unregistered' ? 'inactive' :
                   school.registration_status === 'inactive' ? 'inactive' :
                   school.registration_status || 'inactive'}
                </Badge>
              )}
            </div>

            {/* Stats */}
            <div className="text-sm">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-blue-500" />
                <span>{school.student_count || 0} students</span>
              </div>
            </div>

            {/* Contact Info */}
            {(school.contact_phone || school.email) && (
              <div className="space-y-1 text-sm">
                {school.contact_phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600">{school.contact_phone}</span>
                  </div>
                )}
                {school.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-3 w-3 text-gray-400" />
                    <span className="text-gray-600 truncate">{school.email}</span>
                  </div>
                )}
              </div>
            )}

            {/* Location */}
            {(school.district || school.sub_county) && (
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-3 w-3 text-gray-400" />
                <span className="text-gray-600">
                  {[school.district, school.sub_county].filter(Boolean).join(', ')}
                </span>
              </div>
            )}

            {/* Action Button */}
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full mt-3"
              onClick={() => onViewDetails(school)}
            >
              View Details
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default SchoolsGrid;
