import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  FileText,
  Target,
  AlertTriangle,
  Trophy,
  Eye,
  BookOpen,
  Plus,
  X,
  Loader2,
  AlertCircle
} from 'lucide-react';
import OptimizedPhotoUpload, { PhotoUploadItem } from './OptimizedPhotoUpload';
import {
  validateField,
  validateFieldReport,
  sanitizeFieldReportData,
  FieldReportFormData,
  FieldValidationResult,
  FacilitatorInfo,
  ActivityFeedback,
  EDUCATION_LEVELS,
  calculateTotalStudents,
  calculateTotalRoundTables
} from '@/utils/fieldReportValidation';

// Use the database field_activity_type enum
type FieldActivityType = 'leadership_training' | 'school_visit' | 'community_engagement' | 'assessment' | 'meeting' | 'other';

// Form-specific interface for react-hook-form
interface FormData {
  activity_type: FieldActivityType;
  round_table_sessions_count: number;
  total_students_attended: number;
  students_per_session: number;
  challenges_encountered: string;
  wins_achieved: string;
  general_observations: string;
  lessons_learned: string;
  follow_up_required: boolean;
  follow_up_actions: string;

  // Enhanced fields
  venue_location: string;
  activity_dates: string;
  male_participants: number;
  female_participants: number;
  students_primary: number;
  students_s1: number;
  students_s2: number;
  students_s3: number;
  students_s4: number;
  students_other: number;
  champions_count: number;
  round_tables_primary: number;
  round_tables_s1: number;
  round_tables_s2: number;
  round_tables_s3: number;
  round_tables_s4: number;
  round_tables_other: number;
  introduction: string;
  recommendations: string;
  way_forward: string;
}

interface FieldReportFormProps {
  onSubmit: (data: FieldReportFormData) => void;
  isSubmitting?: boolean;
  defaultValues?: Partial<FieldReportFormData>;
}

const FieldReportForm: React.FC<FieldReportFormProps> = ({
  onSubmit,
  isSubmitting = false,
  defaultValues = {}
}) => {
  const [activities, setActivities] = useState<string[]>(defaultValues.activities_conducted || []);
  const [topics, setTopics] = useState<string[]>(defaultValues.topics_covered || []);
  const [newActivity, setNewActivity] = useState('');
  const [newTopic, setNewTopic] = useState('');
  const [photos, setPhotos] = useState<PhotoUploadItem[]>([]);
  const [fieldValidations, setFieldValidations] = useState<Record<string, FieldValidationResult>>({});
  const [formWarnings, setFormWarnings] = useState<string[]>([]);

  // Enhanced form state
  const [facilitators, setFacilitators] = useState<FacilitatorInfo[]>(defaultValues.facilitators || []);
  const [activityFeedback, setActivityFeedback] = useState<ActivityFeedback[]>(defaultValues.activity_feedback || []);
  const [showEnhancedFields, setShowEnhancedFields] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors }
  } = useForm<FormData>({
    defaultValues: {
      activity_type: defaultValues?.activity_type || 'leadership_training',
      round_table_sessions_count: defaultValues?.round_table_sessions_count || 0,
      total_students_attended: defaultValues?.total_students_attended || 0,
      students_per_session: defaultValues?.students_per_session || 8,
      challenges_encountered: defaultValues?.challenges_encountered || '',
      wins_achieved: defaultValues?.wins_achieved || '',
      general_observations: defaultValues?.general_observations || '',
      lessons_learned: defaultValues?.lessons_learned || '',
      follow_up_required: defaultValues?.follow_up_required || false,
      follow_up_actions: defaultValues?.follow_up_actions || '',

      // Enhanced fields
      venue_location: defaultValues?.venue_location || '',
      activity_dates: defaultValues?.activity_dates || '',
      male_participants: defaultValues?.male_participants || 0,
      female_participants: defaultValues?.female_participants || 0,
      students_primary: defaultValues?.students_primary || 0,
      students_s1: defaultValues?.students_s1 || 0,
      students_s2: defaultValues?.students_s2 || 0,
      students_s3: defaultValues?.students_s3 || 0,
      students_s4: defaultValues?.students_s4 || 0,
      students_other: defaultValues?.students_other || 0,
      champions_count: defaultValues?.champions_count || 0,
      round_tables_primary: defaultValues?.round_tables_primary || 0,
      round_tables_s1: defaultValues?.round_tables_s1 || 0,
      round_tables_s2: defaultValues?.round_tables_s2 || 0,
      round_tables_s3: defaultValues?.round_tables_s3 || 0,
      round_tables_s4: defaultValues?.round_tables_s4 || 0,
      round_tables_other: defaultValues?.round_tables_other || 0,
      introduction: defaultValues?.introduction || '',
      recommendations: defaultValues?.recommendations || '',
      way_forward: defaultValues?.way_forward || '',
    }
  });

  const followUpRequired = watch('follow_up_required');
  const activityType = watch('activity_type');

  // Real-time validation for form fields
  const validateFormField = (fieldName: keyof FieldReportFormData, value: any) => {
    const currentFormData = {
      ...getValues(),
      activities_conducted: activities,
      topics_covered: topics,
      photos: photos as any // Type assertion for PhotoUploadItem[]
    } as FieldReportFormData;
    const validation = validateField(fieldName, value, currentFormData);

    setFieldValidations(prev => ({
      ...prev,
      [fieldName]: validation
    }));

    return validation;
  };

  // Validate field on blur or change
  const handleFieldValidation = (fieldName: keyof FormData) => {
    const value = getValues(fieldName);
    validateFormField(fieldName as keyof FieldReportFormData, value);
  };

  const addActivity = () => {
    if (newActivity.trim()) {
      // Validate the new activity before adding
      const validation = validateField('activities_conducted', [...activities, newActivity.trim()]);
      if (!validation.isValid) {
        setFieldValidations(prev => ({
          ...prev,
          activities_conducted: validation
        }));
        return;
      }

      const updatedActivities = [...activities, newActivity.trim()];
      setActivities(updatedActivities);
      setNewActivity('');

      // Clear any previous validation errors
      setFieldValidations(prev => ({
        ...prev,
        activities_conducted: { isValid: true }
      }));
    }
  };

  const removeActivity = (index: number) => {
    const updatedActivities = activities.filter((_, i) => i !== index);
    setActivities(updatedActivities);
  };

  const addTopic = () => {
    if (newTopic.trim()) {
      // Validate the new topic before adding
      const validation = validateField('topics_covered', [...topics, newTopic.trim()]);
      if (!validation.isValid) {
        setFieldValidations(prev => ({
          ...prev,
          topics_covered: validation
        }));
        return;
      }

      const updatedTopics = [...topics, newTopic.trim()];
      setTopics(updatedTopics);
      setNewTopic('');

      // Clear any previous validation errors
      setFieldValidations(prev => ({
        ...prev,
        topics_covered: { isValid: true }
      }));
    }
  };

  const removeTopic = (index: number) => {
    const updatedTopics = topics.filter((_, i) => i !== index);
    setTopics(updatedTopics);
  };

  // Handle photo changes from OptimizedPhotoUpload
  const handlePhotosChanged = (newPhotos: PhotoUploadItem[]) => {
    setPhotos(newPhotos);
  };

  // Facilitator management functions
  const addFacilitator = () => {
    setFacilitators(prev => [...prev, { name: '', mobile: '', email: '' }]);
  };

  const updateFacilitator = (index: number, field: keyof FacilitatorInfo, value: string) => {
    setFacilitators(prev => prev.map((facilitator, i) =>
      i === index ? { ...facilitator, [field]: value } : facilitator
    ));
  };

  const removeFacilitator = (index: number) => {
    setFacilitators(prev => prev.filter((_, i) => i !== index));
  };

  // Activity feedback management functions
  const addActivityFeedback = () => {
    setActivityFeedback(prev => [...prev, { topic: '', what_worked_well: '', participant_comments: '' }]);
  };

  const updateActivityFeedback = (index: number, field: keyof ActivityFeedback, value: string) => {
    setActivityFeedback(prev => prev.map((feedback, i) =>
      i === index ? { ...feedback, [field]: value } : feedback
    ));
  };

  const removeActivityFeedback = (index: number) => {
    setActivityFeedback(prev => prev.filter((_, i) => i !== index));
  };

  const onFormSubmit = (data: FormData) => {
    // Prepare form data
    const formData: FieldReportFormData = {
      ...data,
      activities_conducted: activities,
      topics_covered: topics,
      photos: photos as any, // Type assertion for PhotoUploadItem[]
      facilitators: facilitators,
      activity_feedback: activityFeedback,
    };

    // Perform comprehensive validation
    const validation = validateFieldReport(formData);

    if (!validation.isValid) {
      // Set field errors for display
      const newFieldValidations: Record<string, FieldValidationResult> = {};
      Object.entries(validation.fieldErrors).forEach(([field, error]) => {
        newFieldValidations[field] = { isValid: false, error };
      });
      setFieldValidations(newFieldValidations);

      // Show general error message
      console.error('Form validation failed:', validation.errors);
      return;
    }

    // Set warnings if any
    if (validation.warnings.length > 0) {
      setFormWarnings(validation.warnings);
    }

    // Sanitize data before submission
    const sanitizedData = sanitizeFieldReportData(formData);

    onSubmit(sanitizedData);
  };

  // Helper component for displaying field validation messages
  const FieldValidationMessage = ({ fieldName }: { fieldName: string }) => {
    const validation = fieldValidations[fieldName];
    if (!validation) return null;

    if (!validation.isValid && validation.error) {
      return (
        <div className="flex items-center gap-1 text-sm text-red-600 mt-1">
          <AlertCircle className="h-3 w-3" />
          {validation.error}
        </div>
      );
    }

    if (validation.warning) {
      return (
        <div className="flex items-center gap-1 text-sm text-amber-600 mt-1">
          <AlertTriangle className="h-3 w-3" />
          {validation.warning}
        </div>
      );
    }

    return null;
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Form-level warnings */}
      {formWarnings.length > 0 && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            <div className="space-y-1">
              {formWarnings.map((warning, index) => (
                <div key={index}>{warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      {/* Activity Type */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Activity Information
          </CardTitle>
          <CardDescription>
            Basic information about today's activities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="activity_type">Activity Type *</Label>
            <Select
              value={activityType}
              onValueChange={(value: FieldActivityType) => setValue('activity_type', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select activity type..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="leadership_training">Leadership Training</SelectItem>
                <SelectItem value="school_visit">School Visit</SelectItem>
                <SelectItem value="community_engagement">Community Engagement</SelectItem>
                <SelectItem value="assessment">Assessment</SelectItem>
                <SelectItem value="meeting">Meeting</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {activityType === 'leadership_training' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="round_table_sessions_count">Round Table Sessions *</Label>
                <Input
                  id="round_table_sessions_count"
                  type="number"
                  min="0"
                  max="50"
                  {...register('round_table_sessions_count', {
                    required: 'Number of sessions is required',
                    min: { value: 0, message: 'Must be 0 or greater' },
                    max: { value: 50, message: 'Must be 50 or less' }
                  })}
                  onBlur={() => handleFieldValidation('round_table_sessions_count')}
                />
                {errors.round_table_sessions_count && (
                  <p className="text-sm text-red-600 mt-1">
                    {errors.round_table_sessions_count.message}
                  </p>
                )}
                <FieldValidationMessage fieldName="round_table_sessions_count" />
              </div>

              <div>
                <Label htmlFor="total_students_attended">Total Students *</Label>
                <Input
                  id="total_students_attended"
                  type="number"
                  min="0"
                  {...register('total_students_attended', { 
                    required: 'Total students is required',
                    min: { value: 0, message: 'Must be 0 or greater' }
                  })}
                />
                {errors.total_students_attended && (
                  <p className="text-sm text-red-600 mt-1">
                    {errors.total_students_attended.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="students_per_session">Students per Session</Label>
                <Input
                  id="students_per_session"
                  type="number"
                  min="1"
                  max="20"
                  {...register('students_per_session', { 
                    min: { value: 1, message: 'Must be at least 1' },
                    max: { value: 20, message: 'Must be 20 or less' }
                  })}
                />
                {errors.students_per_session && (
                  <p className="text-sm text-red-600 mt-1">
                    {errors.students_per_session.message}
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Activities Conducted */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Activities Conducted
          </CardTitle>
          <CardDescription>
            List the specific activities you conducted today
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Add an activity..."
              value={newActivity}
              onChange={(e) => setNewActivity(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addActivity())}
            />
            <Button type="button" onClick={addActivity} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {activities.map((activity, index) => (
              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                {activity}
                <button
                  type="button"
                  onClick={() => removeActivity(index)}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Topics Covered */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Topics Covered
          </CardTitle>
          <CardDescription>
            List the key topics or subjects covered during the session
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Add a topic..."
              value={newTopic}
              onChange={(e) => setNewTopic(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTopic())}
            />
            <Button type="button" onClick={addTopic} size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            {topics.map((topic, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {topic}
                <button
                  type="button"
                  onClick={() => removeTopic(index)}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Challenges, Wins, and Observations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Challenges Encountered
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Describe any challenges or difficulties you faced..."
              {...register('challenges_encountered')}
              rows={4}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-green-500" />
              Wins & Successes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Describe successes, achievements, or positive outcomes..."
              {...register('wins_achieved')}
              rows={4}
            />
          </CardContent>
        </Card>
      </div>

      {/* Observations and Lessons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              General Observations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Share your general observations about the session..."
              {...register('general_observations')}
              rows={4}
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Lessons Learned</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="What did you learn from today's activities?"
              {...register('lessons_learned')}
              rows={4}
            />
          </CardContent>
        </Card>
      </div>

      {/* Follow-up Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Follow-up Actions</CardTitle>
          <CardDescription>
            Indicate if any follow-up actions are required
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="follow_up_required"
              checked={followUpRequired}
              onCheckedChange={(checked) => setValue('follow_up_required', !!checked)}
            />
            <Label htmlFor="follow_up_required">
              Follow-up actions are required
            </Label>
          </div>

          {followUpRequired && (
            <Textarea
              placeholder="Describe the follow-up actions needed..."
              {...register('follow_up_actions')}
              rows={3}
            />
          )}
        </CardContent>
      </Card>

      {/* Photo Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Photo Documentation
          </CardTitle>
          <CardDescription>
            Upload photos to document field activities and observations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OptimizedPhotoUpload
            onPhotosChanged={handlePhotosChanged}
            maxFiles={5}
            compressionOptions={{
              maxWidth: 1920,
              maxHeight: 1080,
              quality: 0.8,
              format: 'jpeg',
              maxSizeKB: 500
            }}
            disabled={isSubmitting}
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={isSubmitting}
        className="w-full"
        size="lg"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Submitting Report...
          </>
        ) : (
          'Submit Field Report'
        )}
      </Button>
    </form>
  );
};

export default FieldReportForm;
